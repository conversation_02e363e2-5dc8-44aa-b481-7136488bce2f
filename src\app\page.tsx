
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft } from 'lucide-react';
import { toolCategories } from '@/lib/tools';

export default function Home() {
  return (
    <div className="flex-1 w-full">
      <section className="w-full py-16 md:py-24">
        <div className="container-full">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-headline font-bold tracking-tighter sm:text-5xl">
              أدوات بالعربي - مجموعة أدوات مجانية شاملة
            </h1>
            <p className="mt-4 max-w-4xl mx-auto text-muted-foreground md:text-xl">
              مجموعة شاملة ومتنامية من الأدوات والحاسبات والمحولات المجانية باللغة العربية. محول التاريخ الهجري والميلادي، حاسبة الزكاة، عداد الكلمات، محول العملات، وأكثر من 50 أداة مفيدة مصممة لتسهيل مهامك اليومية والأكاديمية والمالية.
            </p>
            <div className="mt-6 flex flex-wrap justify-center gap-2 text-sm text-muted-foreground">
              <span className="bg-primary/10 text-primary px-3 py-1 rounded-full">محول التاريخ</span>
              <span className="bg-primary/10 text-primary px-3 py-1 rounded-full">حاسبة الزكاة</span>
              <span className="bg-primary/10 text-primary px-3 py-1 rounded-full">عداد الكلمات</span>
              <span className="bg-primary/10 text-primary px-3 py-1 rounded-full">محول العملات</span>
              <span className="bg-primary/10 text-primary px-3 py-1 rounded-full">أدوات مجانية</span>
            </div>
          </div>

          {toolCategories.map((category) => {
            const availableTools = category.tools;
            if (availableTools.length === 0) return null;

            return (
              <div key={category.slug} className="mb-16">
                <div className="flex items-center gap-4 mb-8">
                  <category.icon className="w-8 h-8 text-primary" />
                  <h2 className="text-3xl font-headline font-bold tracking-tighter">
                    {category.name}
                  </h2>
                </div>
                 <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {availableTools.map((tool) => (
                    <Link
                      key={tool.name}
                      href={tool.path}
                      className="group">
                      <Card className="h-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-primary">
                        <CardHeader>
                          <div className="flex items-center justify-between">
                              <div className="flex items-center gap-4">
                                  {tool.icon && (
                                      <div className="p-2 rounded-full bg-primary/10 text-primary">
                                          <tool.icon className="w-5 h-5" />
                                      </div>
                                  )}
                                  <CardTitle className="font-headline text-lg">{tool.name}</CardTitle>
                              </div>
                              <ArrowLeft className="h-5 w-5 text-muted-foreground transition-transform group-hover:translate-x-[-4px] group-hover:text-primary shrink-0" />
                          </div>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-muted-foreground">
                            {tool.description}
                          </p>
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      </section>
    </div>
  );
}
