export const zatcaQrDecoderContent = {
  title: 'فك تشفير QR Code - ZATCA',
  description: 'أداة متخصصة لفك تشفير رموز QR الخاصة بالفواتير الإلكترونية المتوافقة مع معايير هيئة الزكاة والضريبة والجمارك (ZATCA). تتيح لك هذه الأداة استخراج جميع البيانات المُشفرة في رمز الاستجابة السريعة وعرضها بشكل واضح ومنظم.',
  
  features: [
    'فك تشفير رموز QR المتوافقة مع معايير ZATCA',
    'استخراج بيانات البائع والرقم الضريبي',
    'عرض تاريخ ووقت إصدار الفاتورة',
    'إظهار المبالغ المالية وضريبة القيمة المضافة',
    'التحقق من صحة البيانات المُستخرجة',
    'واجهة سهلة الاستخدام باللغة العربية'
  ],

  howToUse: `
    <h3>كيفية استخدام أداة فك تشفير QR Code</h3>
    <ol>
      <li><strong>الحصول على البيانات:</strong> استخدم أي قارئ QR Code لقراءة الرمز من الفاتورة الإلكترونية</li>
      <li><strong>نسخ البيانات:</strong> انسخ النص المُستخرج من QR Code (البيانات المُشفرة بـ Base64)</li>
      <li><strong>الصق البيانات:</strong> الصق البيانات في المربع النصي المخصص</li>
      <li><strong>فك التشفير:</strong> اضغط على زر "فك التشفير" لاستخراج البيانات</li>
      <li><strong>مراجعة النتائج:</strong> ستظهر جميع بيانات الفاتورة بشكل منظم</li>
    </ol>

    <h3>معايير ZATCA المدعومة</h3>
    <p>تدعم الأداة جميع المعايير المطلوبة من هيئة الزكاة والضريبة والجمارك:</p>
    <ul>
      <li>تنسيق TLV (Tag-Length-Value) للبيانات</li>
      <li>تشفير Base64 للرمز النهائي</li>
      <li>البيانات الأساسية المطلوبة للفاتورة الإلكترونية</li>
      <li>التوافق مع الفواتير الضريبية والمبسطة</li>
    </ul>

    <h3>البيانات المستخرجة</h3>
    <p>تقوم الأداة باستخراج البيانات التالية من QR Code:</p>
    <ul>
      <li><strong>اسم البائع:</strong> الاسم التجاري للمنشأة</li>
      <li><strong>الرقم الضريبي:</strong> رقم التسجيل في ضريبة القيمة المضافة</li>
      <li><strong>تاريخ ووقت الفاتورة:</strong> تاريخ ووقت إصدار الفاتورة بالتفصيل</li>
      <li><strong>المجموع الإجمالي:</strong> إجمالي مبلغ الفاتورة شامل الضريبة</li>
      <li><strong>مبلغ ضريبة القيمة المضافة:</strong> قيمة الضريبة المحتسبة</li>
    </ul>
  `,

  benefits: [
    'التحقق من صحة الفواتير الإلكترونية',
    'مراجعة البيانات المالية بسهولة',
    'التأكد من التوافق مع معايير ZATCA',
    'استخراج البيانات للمراجعة والتدقيق',
    'فهم محتوى QR Code بشكل واضح'
  ],

  technicalSpecs: [
    'يدعم تنسيق TLV (Tag-Length-Value)',
    'فك تشفير Base64 المتقدم',
    'التحقق من صحة البيانات المُستخرجة',
    'عرض البيانات بتنسيق سهل القراءة',
    'دعم كامل للغة العربية'
  ],

  useCases: [
    'التحقق من صحة الفواتير المستلمة',
    'مراجعة البيانات المالية للفواتير',
    'التدقيق والمراجعة المحاسبية',
    'التأكد من التوافق مع معايير ZATCA',
    'استخراج البيانات للأنظمة المحاسبية'
  ],

  faqs: [
    { 
      question: 'ما هو QR Code في الفاتورة الإلكترونية؟', 
      answer: 'QR Code في الفاتورة الإلكترونية هو رمز يحتوي على البيانات الأساسية للفاتورة مُشفرة بتنسيق TLV وBase64 وفقاً لمعايير هيئة الزكاة والضريبة والجمارك.' 
    },
    { 
      question: 'كيف أحصل على البيانات المُشفرة من QR Code؟', 
      answer: 'يمكنك استخدام أي تطبيق قارئ QR Code على هاتفك أو استخدام أداة قارئ QR Code في الموقع لاستخراج البيانات المُشفرة.' 
    },
    { 
      question: 'هل الأداة متوافقة مع جميع أنواع الفواتير؟', 
      answer: 'نعم، الأداة متوافقة مع جميع أنواع الفواتير الإلكترونية المتوافقة مع معايير ZATCA، سواء كانت فواتير ضريبية أو فواتير ضريبية مبسطة.' 
    },
    { 
      question: 'ماذا لو فشلت الأداة في فك التشفير؟', 
      answer: 'إذا فشلت الأداة في فك التشفير، تأكد من أن البيانات المُدخلة صحيحة وكاملة، وأنها مُستخرجة من QR Code متوافق مع معايير ZATCA.' 
    },
    { 
      question: 'هل البيانات آمنة عند استخدام الأداة؟', 
      answer: 'نعم، جميع العمليات تتم محلياً في متصفحك ولا يتم إرسال أي بيانات إلى خوادم خارجية، مما يضمن خصوصية وأمان بياناتك.' 
    }
  ]
};
