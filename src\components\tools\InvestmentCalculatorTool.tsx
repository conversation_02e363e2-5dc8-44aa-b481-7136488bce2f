
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';

const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  initialAmount: requiredNumber().nonnegative("المبلغ الأولي لا يمكن أن يكون سالبًا.").default(1000),
  monthlyContribution: requiredNumber().nonnegative("المساهمة الشهرية لا يمكن أن تكون سالبة.").default(100),
  annualReturn: requiredNumber().nonnegative("العائد السنوي لا يمكن أن يكون سالبًا.").default(7),
  years: requiredNumber().int().positive("مدة الاستثمار يجب أن تكون سنة واحدة على الأقل.").default(10),
  compoundingFrequency: z.enum(['annually', 'monthly']).default('annually'),
});

interface Result {
  futureValue: number;
  totalInvested: number;
  totalInterest: number;
}

export function InvestmentCalculatorTool() {
  const [result, setResult] = useState<Result | null>(null);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      initialAmount: 1000,
      monthlyContribution: 100,
      annualReturn: 7,
      years: 10,
      compoundingFrequency: 'annually',
    },
  });

  function onSubmit(data: z.infer<typeof FormSchema>) {
    const { initialAmount, monthlyContribution, annualReturn, years, compoundingFrequency } = data;
    
    const rate = annualReturn / 100;
    const n = compoundingFrequency === 'annually' ? 1 : 12;
    const t = years;

    // Future value of the initial investment
    const fvInitial = initialAmount * Math.pow(1 + rate / n, n * t);

    // Future value of the monthly contributions (annuity)
    const pmt = monthlyContribution;
    let fvContributions = 0;
    if (pmt > 0) {
        if (compoundingFrequency === 'monthly') {
            const monthlyRate = rate / 12;
            fvContributions = pmt * ((Math.pow(1 + monthlyRate, 12 * t) - 1) / monthlyRate);
        } else { // Annual compounding for monthly contributions
            let total = 0;
            for (let i = 0; i < t; i++) {
                total += (pmt * 12) * Math.pow(1 + rate, t - (i + 1));
            }
            fvContributions = total;
        }
    }
    
    const futureValue = fvInitial + fvContributions;
    const totalInvested = initialAmount + (monthlyContribution * 12 * years);
    const totalInterest = futureValue - totalInvested;

    setResult({ futureValue, totalInvested, totalInterest });
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حاسبة الاستثمار (الفائدة المركبة)</CardTitle>
        <CardDescription>قدّر نمو استثماراتك بمرور الوقت مع قوة الفائدة المركبة.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField name="initialAmount" control={form.control} render={({ field }) => (
                <FormItem><FormLabel>المبلغ الأولي للاستثمار</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
              )}/>
              <FormField name="monthlyContribution" control={form.control} render={({ field }) => (
                <FormItem><FormLabel>المساهمة الشهرية</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
              )}/>
              <FormField name="annualReturn" control={form.control} render={({ field }) => (
                <FormItem><FormLabel>نسبة العائد السنوي (%)</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
              )}/>
              <FormField name="years" control={form.control} render={({ field }) => (
                <FormItem><FormLabel>مدة الاستثمار (بالسنوات)</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
              )}/>
            </div>
             <FormField control={form.control} name="compoundingFrequency" render={({ field }) => (
                <FormItem>
                  <FormLabel>تكرار حساب الفائدة</FormLabel>
                   <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl><SelectTrigger><SelectValue /></SelectTrigger></FormControl>
                      <SelectContent>
                        <SelectItem value="annually">سنويًا</SelectItem>
                        <SelectItem value="monthly">شهريًا</SelectItem>
                      </SelectContent>
                    </Select>
                  <FormMessage />
                </FormItem>
            )}/>
            <Button type="submit" className="w-full">احسب الاستثمار</Button>
          </form>
        </Form>
        {result && (
          <div className="mt-8 text-center">
            <h3 className="text-xl font-headline font-semibold mb-4">قيمة استثمارك المستقبلية (تقديري)</h3>
            <div className="p-6 bg-primary/10 rounded-lg mb-4">
                <p className="text-5xl font-bold font-mono text-primary">
                  {result.futureValue.toLocaleString('ar-SA', { style: 'currency', currency: 'SAR', minimumFractionDigits: 0, numberingSystem: 'latn' })}
                </p>
            </div>
             <div className="grid grid-cols-2 gap-4">
                <div className="p-4 bg-secondary rounded-lg">
                    <p className="text-sm text-muted-foreground">إجمالي المبلغ المستثمر</p>
                    <p className="text-2xl font-bold font-mono">{result.totalInvested.toLocaleString('ar-SA', { style: 'currency', currency: 'SAR', minimumFractionDigits: 0, numberingSystem: 'latn' })}</p>
                </div>
                <div className="p-4 bg-green-500/10 rounded-lg">
                    <p className="text-sm text-green-700">إجمالي الأرباح</p>
                    <p className="text-2xl font-bold font-mono text-green-600">{result.totalInterest.toLocaleString('ar-SA', { style: 'currency', currency: 'SAR', minimumFractionDigits: 0, numberingSystem: 'latn' })}</p>
                </div>
            </div>
            <p className="text-xs text-muted-foreground mt-4">
              هذا الرقم هو تقدير بناءً على المدخلات. النتائج الفعلية قد تختلف.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
