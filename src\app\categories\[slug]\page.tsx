
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { toolCategories } from '@/lib/tools';
import { PageHeader } from '@/components/PageHeader';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft } from 'lucide-react';
import type { Metadata, ResolvingMetadata } from 'next';
import { JsonLd } from '@/components/JsonLd';


const siteUrl = process.env.NEXT_PUBLIC_SITE_URL;

type Props = {
  params: { slug: string };
};

export async function generateMetadata(
  { params }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const { slug } = params;
  const category = toolCategories.find(c => c.slug === slug);

  if (!category) {
    return {
      title: 'قسم غير موجود',
    };
  }

  const currentYear = new Date().getFullYear();
  const previousImages = (await parent).openGraph?.images || [];
  const categoryUrl = siteUrl ? `${siteUrl}/categories/${slug}` : `/categories/${slug}`;
  const enhancedDescription = `${category.description} - مجموعة أدوات مجانية ${currentYear}`;

  // Generate keywords for the category
  const keywords = [
    category.name,
    'أدوات عربية',
    'حاسبات مجانية',
    'أدوات مجانية',
    `أدوات ${currentYear}`,
    'Arabic tools',
    'free calculators',
    ...category.tools.slice(0, 5).map(tool => tool.name)
  ];

  return {
    title: `${category.name} - مجموعة أدوات مجانية ${currentYear}`,
    description: enhancedDescription,
    keywords: keywords.join(', '),
    alternates: {
      canonical: `/categories/${slug}`,
    },
    openGraph: {
      title: `${category.name} | أدوات بالعربي ${currentYear}`,
      description: enhancedDescription,
      url: categoryUrl,
      images: [...previousImages],
      type: 'website',
      locale: 'ar_SA',
      siteName: 'أدوات بالعربي',
    },
    twitter: {
      card: 'summary_large_image',
      title: `${category.name} | أدوات بالعربي ${currentYear}`,
      description: enhancedDescription,
      images: [...previousImages],
      site: '@adawat_org',
      creator: '@adawat_org',
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}


export default async function CategoryPage({ params }: { params: { slug: string } }) {
  const { slug } = params;
  const category = toolCategories.find(c => c.slug === slug);

  if (!category) {
    notFound();
  }

  const breadcrumbJsonLd: any = siteUrl ? {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: 'الرئيسية',
        item: siteUrl,
      },
      {
        '@type': 'ListItem',
        position: 2,
        name: category.name,
        item: `${siteUrl}/categories/${slug}`,
      },
    ],
  } : null;

  // Category collection structured data
  const categoryJsonLd: any = siteUrl ? {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: category.name,
    description: category.description,
    url: `${siteUrl}/categories/${slug}`,
    mainEntity: {
      '@type': 'ItemList',
      numberOfItems: category.tools.length,
      itemListElement: category.tools
        .slice(0, 10) // Limit to first 10 tools for performance
        .map((tool, index) => ({
          '@type': 'ListItem',
          position: index + 1,
          item: {
            '@type': 'WebApplication',
            name: tool.name,
            description: tool.description,
            url: `${siteUrl}/tools/${tool.slug}`,
            applicationCategory: 'UtilityApplication'
          }
        }))
    },
    provider: {
      '@type': 'Organization',
      name: 'أدوات بالعربي',
      url: siteUrl
    }
  } : null;

  return (
    <div className="w-full">
      {breadcrumbJsonLd && <JsonLd data={breadcrumbJsonLd} />}
      {categoryJsonLd && <JsonLd data={categoryJsonLd} />}
      <PageHeader
        title={category.name}
        description={category.description}
      />
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mt-12">
        {category.tools.map(tool => (
          <Link key={tool.path} href={tool.path} className="group">
            <Card className="h-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-primary">
              <CardHeader>
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        {tool.icon && (
                            <div className="p-2 rounded-full bg-primary/10 text-primary">
                                <tool.icon className="w-5 h-5" />
                            </div>
                        )}
                        <CardTitle className="font-headline text-lg">{tool.name}</CardTitle>
                    </div>
                    <ArrowLeft className="h-5 w-5 text-muted-foreground transition-transform group-hover:translate-x-[-4px] group-hover:text-primary shrink-0" />
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  {tool.description}
                </p>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
}

export async function generateStaticParams() {
  return toolCategories.map(category => ({
    slug: category.slug,
  }));
}
