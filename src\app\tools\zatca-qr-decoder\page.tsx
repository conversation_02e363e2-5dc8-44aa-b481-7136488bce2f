import { Metadata } from 'next';
import { ZatcaQrDecoderTool } from '@/components/tools/ZatcaQrDecoderTool';
import { getToolBySlug } from '@/lib/tools';
import { notFound } from 'next/navigation';

const toolSlug = 'zatca-qr-decoder';

export async function generateMetadata(): Promise<Metadata> {
  const tool = getToolBySlug(toolSlug);
  
  if (!tool) {
    return {
      title: 'أداة غير موجودة',
    };
  }

  return {
    title: `${tool.name} | جامع الأدوات`,
    description: tool.description,
    keywords: [
      'فك تشفير QR Code',
      'ZATCA',
      'هيئة الزكاة والضريبة والجمارك',
      'الفواتير الإلكترونية',
      'TLV',
      'Base64',
      'رمز الاستجابة السريعة',
      'فك التشفير',
      'التحقق من الفاتورة'
    ],
    openGraph: {
      title: `${tool.name} | جامع الأدوات`,
      description: tool.description,
      type: 'website',
    },
  };
}

export default function ZatcaQrDecoderPage() {
  const tool = getToolBySlug(toolSlug);
  
  if (!tool) {
    notFound();
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {tool.name}
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {tool.description}
          </p>
        </div>
        
        <div className="flex justify-center">
          <ZatcaQrDecoderTool />
        </div>

        <div className="mt-12 max-w-3xl mx-auto">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-blue-900 mb-4">كيفية استخدام الأداة</h2>
            <ol className="list-decimal list-inside space-y-2 text-blue-800">
              <li>احصل على البيانات المُشفرة من QR Code الخاص بالفاتورة الإلكترونية</li>
              <li>الصق البيانات في المربع النصي أعلاه</li>
              <li>اضغط على زر "فك التشفير"</li>
              <li>ستظهر جميع بيانات الفاتورة بشكل واضح ومنظم</li>
            </ol>
          </div>

          <div className="mt-6 bg-green-50 border border-green-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-green-900 mb-4">المعايير المدعومة</h2>
            <ul className="list-disc list-inside space-y-2 text-green-800">
              <li>متوافق مع معايير هيئة الزكاة والضريبة والجمارك (ZATCA)</li>
              <li>يدعم تنسيق TLV (Tag-Length-Value)</li>
              <li>فك تشفير البيانات المُرمزة بـ Base64</li>
              <li>استخراج جميع البيانات المطلوبة للفاتورة الإلكترونية</li>
            </ul>
          </div>

          <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-yellow-900 mb-4">البيانات المستخرجة</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-yellow-800">
              <div>
                <h3 className="font-medium mb-2">بيانات البائع:</h3>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>اسم البائع</li>
                  <li>الرقم الضريبي</li>
                </ul>
              </div>
              <div>
                <h3 className="font-medium mb-2">بيانات الفاتورة:</h3>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>تاريخ ووقت الإصدار</li>
                  <li>المجموع الإجمالي</li>
                  <li>مبلغ ضريبة القيمة المضافة</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
