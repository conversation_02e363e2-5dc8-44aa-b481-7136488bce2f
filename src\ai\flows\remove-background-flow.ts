'use server';
/**
 * @fileOverview Removes the background from an image using a free third-party API.
 * 
 * - removeBackgroundFromImage - A function that takes an image data URI and returns a new data URI with a transparent background.
 * - RemoveBackgroundInput - The input type for the function.
 * - RemoveBackgroundOutput - The return type for the function.
 */

import { ai } from '@/ai/genkit';
import { z } from 'genkit';
import { Buffer } from 'buffer';

const RemoveBackgroundInputSchema = z.object({
  photoDataUri: z
    .string()
    .describe(
      "A photo of a person or object, as a data URI that must include a MIME type and use Base64 encoding. Expected format: 'data:<mimetype>;base64,<encoded_data>'."
    ),
});
export type RemoveBackgroundInput = z.infer<typeof RemoveBackgroundInputSchema>;

const RemoveBackgroundOutputSchema = z.object({
  processedImageUri: z.string().describe('The processed image with a transparent background as a data URI.'),
});
export type RemoveBackgroundOutput = z.infer<typeof RemoveBackgroundOutputSchema>;

// This function will be called from the client-side component.
export async function removeBackgroundFromImage(input: RemoveBackgroundInput): Promise<RemoveBackgroundOutput> {
  return removeBackgroundFlow(input);
}

const removeBackgroundFlow = ai.defineFlow(
  {
    name: 'removeBackgroundFlow',
    inputSchema: RemoveBackgroundInputSchema,
    outputSchema: RemoveBackgroundOutputSchema,
  },
  async (input) => {
    try {
      // The API expects the image data as a Buffer.
      // We extract the Base64 part of the data URI and convert it.
      const base64Data = input.photoDataUri.split(',')[1];
      const imageBuffer = Buffer.from(base64Data, 'base64');
      
      // Use FormData to send the image as a multipart/form-data request,
      // which is what the remove.bg API and its wrappers expect.
      const formData = new FormData();
      formData.append('size', 'auto');
      formData.append('image_file', new Blob([imageBuffer]), 'image.png');

      const response = await fetch('https://api.remove.bg/v1.0/removebg', {
        method: 'POST',
        headers: {
            // Note: This uses a publicly available, limited-use API key for demonstration.
            // For production, it's recommended to get your own key from remove.bg.
          'X-Api-Key': '6rMrJ3RQXHugNWqtj1nUU1N1',
        },
        body: formData,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error:', errorText);
        throw new Error(`Failed to remove background. API responded with status: ${response.status}`);
      }

      // The API returns the processed image as a binary blob.
      const processedImageBlob = await response.blob();
      const processedImageBuffer = await processedImageBlob.arrayBuffer();
      
      // Convert the result back to a data URI to send to the client.
      const processedBase64 = Buffer.from(processedImageBuffer).toString('base64');
      const processedImageUri = `data:${processedImageBlob.type};base64,${processedBase64}`;

      return {
        processedImageUri,
      };

    } catch (error) {
      console.error('Error in removeBackgroundFlow:', error);
      throw new Error('An unexpected error occurred while processing the image.');
    }
  }
);
