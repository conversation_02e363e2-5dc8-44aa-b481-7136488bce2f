'use client';

import { useState, useRef, useEffect } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Upload, Download, Trash2, AlertTriangle, FileText, Merge, Loader2, X, GripVertical, ChevronUp, ChevronDown } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

// Declare global types for PDF-lib
declare global {
  interface Window {
    PDFLib: any;
  }
}

interface PdfFile {
  file: File;
  id: string;
  name: string;
  size: string;
}

export function PdfMergerTool() {
  const [selectedFiles, setSelectedFiles] = useState<PdfFile[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isMerging, setIsMerging] = useState(false);
  const [pdfLibLoaded, setPdfLibLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [progressText, setProgressText] = useState('');
  const [mergedPdfBlob, setMergedPdfBlob] = useState<Blob | null>(null);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Load PDF-lib library
  useEffect(() => {
    const loadPdfLib = () => {
      if (typeof window !== 'undefined' && !window.PDFLib) {
        setIsLoading(true);
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf-lib/1.17.1/pdf-lib.min.js';
        script.onload = () => {
          if (window.PDFLib) {
            setPdfLibLoaded(true);
            setIsLoading(false);
          }
        };
        script.onerror = () => {
          setError('فشل في تحميل مكتبة PDF-lib. يرجى إعادة تحميل الصفحة.');
          setIsLoading(false);
        };
        document.head.appendChild(script);
      } else if (window.PDFLib) {
        setPdfLibLoaded(true);
      }
    };

    loadPdfLib();
  }, []);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const generateId = (): string => {
    return Math.random().toString(36).substr(2, 9);
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    const pdfFiles = Array.from(files).filter(file => file.type === 'application/pdf');

    if (pdfFiles.length === 0) {
      setError('يرجى اختيار ملفات PDF صالحة.');
      return;
    }

    // Check file sizes (max 10MB per file)
    const oversizedFiles = pdfFiles.filter(file => file.size > 10 * 1024 * 1024);
    if (oversizedFiles.length > 0) {
      setError('بعض الملفات كبيرة جداً. يرجى اختيار ملفات أصغر من 10 ميجابايت.');
      return;
    }

    const newFiles: PdfFile[] = pdfFiles.map(file => ({
      file,
      id: generateId(),
      name: file.name,
      size: formatFileSize(file.size)
    }));

    // Check for duplicates
    const existingNames = selectedFiles.map(f => f.name);
    const uniqueFiles = newFiles.filter(f => !existingNames.includes(f.name));

    if (uniqueFiles.length === 0) {
      setError('جميع الملفات المختارة موجودة بالفعل.');
      return;
    }

    setSelectedFiles(prev => [...prev, ...uniqueFiles]);
    setError(null);
    setSuccess(null);
    setMergedPdfBlob(null);

    toast({
      title: 'تم إضافة الملفات',
      description: `تم إضافة ${uniqueFiles.length} ملف PDF جديد.`,
    });

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeFile = (id: string) => {
    setSelectedFiles(prev => prev.filter(file => file.id !== id));
    setError(null);
    setSuccess(null);
    setMergedPdfBlob(null);
  };

  const clearAllFiles = () => {
    setSelectedFiles([]);
    setError(null);
    setSuccess(null);
    setMergedPdfBlob(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const moveFile = (fromIndex: number, toIndex: number) => {
    const newFiles = [...selectedFiles];
    const [movedFile] = newFiles.splice(fromIndex, 1);
    newFiles.splice(toIndex, 0, movedFile);
    setSelectedFiles(newFiles);
    setError(null);
    setSuccess(null);
    setMergedPdfBlob(null);
  };

  const moveFileUp = (index: number) => {
    if (index > 0) {
      moveFile(index, index - 1);
      toast({
        title: 'تم تحريك الملف',
        description: 'تم تحريك الملف لأعلى بنجاح.',
      });
    }
  };

  const moveFileDown = (index: number) => {
    if (index < selectedFiles.length - 1) {
      moveFile(index, index + 1);
      toast({
        title: 'تم تحريك الملف',
        description: 'تم تحريك الملف لأسفل بنجاح.',
      });
    }
  };

  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/html', '');
  };

  const handleDragEnd = () => {
    setDraggedIndex(null);
  };

  const handleFileItemDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleFileItemDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    if (draggedIndex !== null && draggedIndex !== dropIndex) {
      moveFile(draggedIndex, dropIndex);
      toast({
        title: 'تم إعادة ترتيب الملف',
        description: 'تم تغيير ترتيب الملف بنجاح.',
      });
    }
    setDraggedIndex(null);
  };

  const mergePDFs = async () => {
    if (selectedFiles.length < 2 || !pdfLibLoaded) return;

    setIsMerging(true);
    setError(null);
    setSuccess(null);
    setProgress(0);
    setProgressText('بدء عملية الدمج...');

    try {
      const { PDFDocument } = window.PDFLib;
      const mergedPdf = await PDFDocument.create();

      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i];
        setProgressText(`معالجة ${file.name}...`);

        const arrayBuffer = await file.file.arrayBuffer();
        const pdf = await PDFDocument.load(arrayBuffer);
        const pages = await mergedPdf.copyPages(pdf, pdf.getPageIndices());
        
        pages.forEach((page: any) => mergedPdf.addPage(page));
        
        // Update progress
        const progress = ((i + 1) / selectedFiles.length) * 100;
        setProgress(Math.min(progress, 100));
      }

      setProgressText('حفظ الملف المدموج...');
      const mergedPdfBytes = await mergedPdf.save();
      
      // Create blob for download
      const blob = new Blob([mergedPdfBytes], { type: 'application/pdf' });
      setMergedPdfBlob(blob);
      
      setProgress(100);
      setProgressText('تم الدمج بنجاح!');
      setSuccess(`تم دمج ${selectedFiles.length} ملف PDF بنجاح! يمكنك الآن تحميل الملف المدموج.`);
      
    } catch (err) {
      console.error('PDF merge error:', err);
      setError('فشل في دمج ملفات PDF. يرجى التأكد من أن جميع الملفات صالحة وغير محمية بكلمة مرور.');
    } finally {
      setIsMerging(false);
      setTimeout(() => {
        setProgress(0);
        setProgressText('');
      }, 3000);
    }
  };

  const downloadMergedPDF = () => {
    if (mergedPdfBlob) {
      const url = URL.createObjectURL(mergedPdfBlob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'merged_document.pdf';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast({
        title: 'تم التحميل',
        description: 'تم تحميل الملف المدموج بنجاح.',
      });
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    const files = Array.from(e.dataTransfer.files).filter(file => file.type === 'application/pdf');
    
    if (files.length === 0) {
      setError('يرجى سحب ملفات PDF صالحة فقط.');
      return;
    }

    // Simulate file input change
    const event = {
      target: {
        files: files
      }
    } as any;
    
    handleFileChange(event);
  };

  if (isLoading) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="flex items-center justify-center p-8">
          <div className="flex items-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>جاري تحميل مكتبة PDF...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!pdfLibLoaded) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="p-8">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              فشل في تحميل مكتبة PDF. يرجى إعادة تحميل الصفحة.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-2 text-2xl">
            <Merge className="h-8 w-8 text-blue-600" />
            أداة دمج ملفات PDF
          </CardTitle>
          <CardDescription>
            قم بدمج عدة ملفات PDF في ملف واحد بسهولة وأمان. جميع العمليات تتم محلياً في متصفحك.
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Upload Section */}
      <Card>
        <CardContent className="p-6">
          <div
            className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors"
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          >
            <div className="flex flex-col items-center gap-4">
              <div className="p-4 bg-blue-50 rounded-full">
                <Upload className="h-8 w-8 text-blue-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">اختر ملفات PDF للدمج</h3>
                <p className="text-gray-600 mb-4">اسحب وأفلت الملفات هنا أو انقر للاختيار</p>
                <Button
                  onClick={() => fileInputRef.current?.click()}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  اختر ملفات PDF
                </Button>
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept=".pdf"
                  onChange={handleFileChange}
                  className="hidden"
                />
              </div>
              <p className="text-sm text-gray-500">
                الحد الأقصى: 10 ميجابايت لكل ملف • يدعم ملفات PDF فقط
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Success Alert */}
      {success && (
        <Alert className="border-green-200 bg-green-50">
          <Download className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">{success}</AlertDescription>
        </Alert>
      )}

      {/* Progress Bar */}
      {isMerging && (
        <Card>
          <CardContent className="p-6">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>{progressText}</span>
                <span>{Math.round(progress)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Files List */}
      {selectedFiles.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">
                  الملفات المختارة ({selectedFiles.length})
                </CardTitle>
                <CardDescription className="text-sm text-gray-600 mt-1">
                  يمكنك إعادة ترتيب الملفات بالسحب والإفلات أو باستخدام أزرار التحريك
                </CardDescription>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={clearAllFiles}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                مسح الكل
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-3">
              {selectedFiles.map((file, index) => (
                <div
                  key={file.id}
                  draggable
                  onDragStart={(e) => handleDragStart(e, index)}
                  onDragEnd={handleDragEnd}
                  onDragOver={(e) => handleFileItemDragOver(e, index)}
                  onDrop={(e) => handleFileItemDrop(e, index)}
                  className={`flex items-center justify-between p-4 rounded-lg border transition-all duration-200 ${
                    draggedIndex === index
                      ? 'bg-blue-100 border-blue-300 shadow-lg opacity-50'
                      : 'bg-gray-50 hover:bg-gray-100'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <GripVertical className="h-4 w-4 text-gray-400 cursor-move" />
                      <div className="p-2 bg-red-100 rounded">
                        <FileText className="h-5 w-5 text-red-600" />
                      </div>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{file.name}</p>
                      <p className="text-sm text-gray-500">{file.size}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">#{index + 1}</span>

                    {/* Move Up/Down Buttons */}
                    <div className="flex flex-col gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => moveFileUp(index)}
                        disabled={index === 0}
                        className="h-6 w-6 p-0 text-gray-500 hover:text-blue-600 hover:bg-blue-50 disabled:opacity-30"
                        title="تحريك لأعلى"
                      >
                        <ChevronUp className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => moveFileDown(index)}
                        disabled={index === selectedFiles.length - 1}
                        className="h-6 w-6 p-0 text-gray-500 hover:text-blue-600 hover:bg-blue-50 disabled:opacity-30"
                        title="تحريك لأسفل"
                      >
                        <ChevronDown className="h-3 w-3" />
                      </Button>
                    </div>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(file.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      title="حذف الملف"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      {selectedFiles.length > 0 && (
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                onClick={mergePDFs}
                disabled={selectedFiles.length < 2 || isMerging}
                className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-300"
                size="lg"
              >
                {isMerging ? (
                  <>
                    <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                    جاري الدمج...
                  </>
                ) : (
                  <>
                    <Merge className="h-5 w-5 mr-2" />
                    دمج {selectedFiles.length} ملفات
                  </>
                )}
              </Button>

              {mergedPdfBlob && (
                <Button
                  onClick={downloadMergedPDF}
                  className="flex-1 bg-blue-600 hover:bg-blue-700"
                  size="lg"
                >
                  <Download className="h-5 w-5 mr-2" />
                  تحميل الملف المدموج
                </Button>
              )}
            </div>

            {selectedFiles.length < 2 && (
              <p className="text-sm text-gray-500 mt-2 text-center">
                يجب اختيار ملفين على الأقل للدمج
              </p>
            )}
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">كيفية الاستخدام</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="font-semibold text-gray-900">خطوات الدمج:</h4>
              <ol className="space-y-2 text-sm text-gray-600">
                <li className="flex items-start gap-2">
                  <span className="flex-shrink-0 w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">1</span>
                  اختر ملفات PDF التي تريد دمجها
                </li>
                <li className="flex items-start gap-2">
                  <span className="flex-shrink-0 w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">2</span>
                  رتب الملفات حسب الترتيب المطلوب
                </li>
                <li className="flex items-start gap-2">
                  <span className="flex-shrink-0 w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">3</span>
                  انقر على "دمج الملفات"
                </li>
                <li className="flex items-start gap-2">
                  <span className="flex-shrink-0 w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">4</span>
                  حمل الملف المدموج
                </li>
              </ol>
            </div>

            <div className="space-y-4">
              <h4 className="font-semibold text-gray-900">ملاحظات مهمة:</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-start gap-2">
                  <span className="text-green-600">✓</span>
                  جميع العمليات تتم محلياً في متصفحك
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-green-600">✓</span>
                  لا يتم رفع ملفاتك إلى أي خادم
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-green-600">✓</span>
                  يدعم ملفات PDF بحجم يصل إلى 10 ميجابايت
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-green-600">✓</span>
                  يحافظ على جودة الملفات الأصلية
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
