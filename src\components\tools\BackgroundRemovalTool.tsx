
'use client';

import { useState, useRef, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Upload, Download, Trash2, AlertTriangle, ImageIcon, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { removeBackgroundFromImage } from '@/ai/flows/remove-background-flow';
import Image from 'next/image';

const DAILY_LIMIT = 5;

const getUsageData = () => {
    if (typeof window === 'undefined') {
        return { count: 0, date: new Date().toISOString().split('T')[0] };
    }
    const data = localStorage.getItem('bgRemoveUsage');
    if (data) {
        try {
            return JSON.parse(data);
        } catch (e) {
            return { count: 0, date: new Date().toISOString().split('T')[0] };
        }
    }
    return { count: 0, date: new Date().toISOString().split('T')[0] };
};

const setUsageData = (count: number) => {
    if (typeof window !== 'undefined') {
        const date = new Date().toISOString().split('T')[0];
        localStorage.setItem('bgRemoveUsage', JSON.stringify({ count, date }));
    }
};

export function BackgroundRemovalTool() {
  const { toast } = useToast();
  const [originalImage, setOriginalImage] = useState<string | null>(null);
  const [processedImage, setProcessedImage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [usageCount, setUsageCount] = useState(0);
  const [isLimitReached, setIsLimitReached] = useState(false);

  const isAdmin = process.env.NEXT_PUBLIC_ADMIN_BYPASS_LIMIT === 'true';

  useEffect(() => {
    if (isAdmin) return;

    const usage = getUsageData();
    const today = new Date().toISOString().split('T')[0];
    if (usage.date === today) {
        setUsageCount(usage.count);
        if (usage.count >= DAILY_LIMIT) {
            setIsLimitReached(true);
        }
    } else {
        setUsageData(0);
        setUsageCount(0);
    }
  }, [isAdmin]);

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (isLimitReached && !isAdmin) {
        setError('لقد وصلت إلى الحد الأقصى للاستخدام اليومي (5 صور). يرجى المحاولة مرة أخرى غدًا.');
        return;
    }
    
    setError(null);
    setOriginalImage(null);
    setProcessedImage(null);
    setIsProcessing(true);

    const file = event.target.files?.[0];
    if (!file) {
      setIsProcessing(false);
      return;
    }
    if (!file.type.startsWith('image/')) {
      setError('يرجى اختيار ملف صورة صالح.');
      setIsProcessing(false);
      return;
    }
    if (file.size > 5 * 1024 * 1024) { 
      setError('حجم الصورة كبير. يرجى اختيار صورة أصغر من 5 ميجابايت.');
      setIsProcessing(false);
      return;
    }
    
    const reader = new FileReader();
    reader.onload = async (e) => {
      const dataUri = e.target?.result as string;
      setOriginalImage(dataUri);
      
      try {
        const result = await removeBackgroundFromImage({ photoDataUri: dataUri });
        setProcessedImage(result.processedImageUri);
        
        if (!isAdmin) {
          const newCount = usageCount + 1;
          setUsageCount(newCount);
          setUsageData(newCount);
          if (newCount >= DAILY_LIMIT) {
              setIsLimitReached(true);
          }
        }
      } catch (err) {
        console.error("Background removal error:", err);
        setError(err instanceof Error ? err.message : 'فشلت معالجة الصورة. الرجاء المحاولة مرة أخرى.');
      } finally {
        setIsProcessing(false);
      }
    };
    reader.readAsDataURL(file);
  };

  const downloadImage = () => {
    if (!processedImage) return;
    const link = document.createElement('a');
    link.download = 'background-removed.png';
    link.href = processedImage;
    link.click();
    toast({ title: "تم تحميل الصورة" });
  };

  const resetTool = () => {
    setOriginalImage(null);
    setProcessedImage(null);
    setError(null);
    setIsProcessing(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  const remainingAttempts = DAILY_LIMIT - usageCount;
  const finalIsLimitReached = isLimitReached && !isAdmin;

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ImageIcon className="h-6 w-6" />
          أداة إزالة خلفية الصور (جودة عالية)
        </CardTitle>
        <CardDescription>
          أزل خلفية صورك بدقة عالية. تعمل الأداة بشكل ممتاز مع صور الأشخاص والمنتجات وغيرها.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {!originalImage && (
          <div className="mt-4 p-8 border-dashed border-2 rounded-lg text-center">
            <ImageIcon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <Button onClick={() => fileInputRef.current?.click()} size="lg" disabled={finalIsLimitReached || isProcessing}>
              {isProcessing ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  جاري المعالجة...
                </>
              ) : (
                <>
                  <Upload className="ml-2 h-4 w-4" />
                  اختر صورة من جهازك
                </>
              )}
            </Button>
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              accept="image/*"
              onChange={handleFileChange}
              disabled={finalIsLimitReached || isProcessing}
            />
            <p className="text-sm text-muted-foreground mt-2">
              {isAdmin
                ? 'لديك صلاحيات غير محدودة.'
                : finalIsLimitReached
                ? 'لقد وصلت للحد الأقصى اليومي.'
                : `لديك ${remainingAttempts} محاولات متبقية اليوم.`
              }
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              الحد الأقصى لحجم الملف: 5 ميجابايت.
            </p>
          </div>
        )}

        {error && (
          <Alert variant="destructive" className="mt-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>خطأ</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {originalImage && (
          <div className="mt-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
              <div>
                <h3 className="text-lg font-semibold mb-2 text-center">الصورة الأصلية</h3>
                <div className="w-full min-h-[300px] max-h-[400px] flex items-center justify-center rounded-lg border">
                  <Image src={originalImage} alt="الصورة الأصلية" width={400} height={400} className="max-w-full max-h-[400px] object-contain" />
                </div>
              </div>
              <div className="relative">
                <h3 className="text-lg font-semibold mb-2 text-center">بعد إزالة الخلفية</h3>
                <div
                  className="w-full min-h-[300px] max-h-[400px] flex items-center justify-center rounded-lg border"
                  style={{
                    backgroundImage: 'linear-gradient(45deg, #f0f0f0 25%, transparent 25%), linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #f0f0f0 75%), linear-gradient(-45deg, transparent 75%, #f0f0f0 75%)',
                    backgroundSize: '20px 20px',
                    backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px'
                  }}
                >
                  {isProcessing && <Loader2 className="h-8 w-8 animate-spin text-primary" />}
                  {!isProcessing && processedImage && <Image src={processedImage} alt="بعد إزالة الخلفية" width={400} height={400} className="max-w-full max-h-[400px] object-contain" />}
                  {!isProcessing && !processedImage && !error && <p className="text-muted-foreground">في انتظار الصورة...</p>}
                </div>
              </div>
            </div>

            <div className="flex flex-wrap gap-2 justify-center">
              <Button onClick={downloadImage} disabled={!processedImage || isProcessing}>
                <Download className="ml-2 h-4 w-4" />
                تحميل الصورة (PNG)
              </Button>
              <Button variant="outline" onClick={resetTool} disabled={isProcessing}>
                <Trash2 className="ml-2 h-4 w-4" />
                صورة جديدة
              </Button>
            </div>
             <p className="text-sm text-muted-foreground mt-2 text-center">
              {isAdmin
                ? 'لديك صلاحيات غير محدودة.'
                : finalIsLimitReached
                ? 'لقد وصلت للحد الأقصى اليومي.'
                : `تبقى لديك ${remainingAttempts} محاولات.`
              }
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
